package com.myhispreadnlp.api.newcognitionspring.example;

import com.myhispreadnlp.api.newcognitionspring.common.utils.JsonUtils;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatCompletion;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatCompletionChunk;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 展示如何使用ChatCompletion和ChatCompletionChunk类进行赋值和序列化
 */
public class ChatCompletionUsageExample {

    /**
     * 示例1：创建简单的ChatCompletion响应
     */
    public static void example1_SimpleChatCompletion() {
        System.out.println("=== 示例1：简单的ChatCompletion响应 ===");
        
        // 创建ChatCompletion对象
        ChatCompletion chatCompletion = new ChatCompletion();
        
        // 设置基本字段
        chatCompletion.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", "").substring(0, 29));
        chatCompletion.setObject("chat.completion");
        chatCompletion.setCreated((int) Instant.now().getEpochSecond());
        chatCompletion.setModel("gpt-4");
        chatCompletion.setSystemFingerprint("fp_12345678");
        
        // 创建choices列表
        List<ChatCompletion.ChatCompletionChoice> choices = new ArrayList<>();
        
        // 创建第一个choice
        ChatCompletion.ChatCompletionChoice choice = new ChatCompletion.ChatCompletionChoice();
        choice.setIndex(0);
        choice.setFinishReason("stop");
        
        // 创建message
        ChatCompletion.ChatMessage message = new ChatCompletion.ChatMessage();
        message.setRole("assistant");
        message.setContent("你好！我是AI助手，很高兴为您服务。");
        
        choice.setMessage(message);
        choices.add(choice);
        chatCompletion.setChoices(choices);
        
        // 创建usage信息
        ChatCompletion.Usage usage = new ChatCompletion.Usage();
        usage.setPromptTokens(8);
        usage.setCompletionTokens(15);
        usage.setTotalTokens(23);
        
        // 设置详细的token信息
        ChatCompletion.PromptTokensDetails promptDetails = new ChatCompletion.PromptTokensDetails();
        promptDetails.setCachedTokens(0);
        promptDetails.setAudioTokens(0);
        usage.setPromptTokensDetails(promptDetails);
        
        ChatCompletion.CompletionTokensDetails completionDetails = new ChatCompletion.CompletionTokensDetails();
        completionDetails.setReasoningTokens(0);
        completionDetails.setAudioTokens(0);
        usage.setCompletionTokensDetails(completionDetails);
        
        chatCompletion.setUsage(usage);
        
        // 序列化为JSON并打印
        String json = JsonUtils.toJSONString(chatCompletion);
        System.out.println(json);
        System.out.println();
    }

    /**
     * 示例2：创建包含工具调用的ChatCompletion响应
     */
    public static void example2_ChatCompletionWithToolCall() {
        System.out.println("=== 示例2：包含工具调用的ChatCompletion响应 ===");
        
        ChatCompletion chatCompletion = new ChatCompletion();
        
        // 设置基本字段
        chatCompletion.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", "").substring(0, 29));
        chatCompletion.setObject("chat.completion");
        chatCompletion.setCreated((int) Instant.now().getEpochSecond());
        chatCompletion.setModel("gpt-4");
        
        // 创建choices
        List<ChatCompletion.ChatCompletionChoice> choices = new ArrayList<>();
        ChatCompletion.ChatCompletionChoice choice = new ChatCompletion.ChatCompletionChoice();
        choice.setIndex(0);
        choice.setFinishReason("tool_calls");
        
        // 创建message with tool calls
        ChatCompletion.ChatMessage message = new ChatCompletion.ChatMessage();
        message.setRole("assistant");
        message.setContent(null); // 当有tool_calls时，content通常为null
        
        // 创建tool calls
        List<ChatCompletion.ToolCall> toolCalls = new ArrayList<>();
        ChatCompletion.ToolCall toolCall = new ChatCompletion.ToolCall();
        toolCall.setId("call_" + UUID.randomUUID().toString().replace("-", "").substring(0, 24));
        toolCall.setType("function");
        
        ChatCompletion.Function function = new ChatCompletion.Function();
        function.setName("get_weather");
        function.setArguments("{\"location\": \"北京\", \"unit\": \"celsius\"}");
        toolCall.setFunction(function);
        
        toolCalls.add(toolCall);
        message.setToolCalls(toolCalls);
        choice.setMessage(message);
        
        choices.add(choice);
        chatCompletion.setChoices(choices);
        
        // 创建usage信息
        ChatCompletion.Usage usage = new ChatCompletion.Usage();
        usage.setPromptTokens(20);
        usage.setCompletionTokens(35);
        usage.setTotalTokens(55);
        chatCompletion.setUsage(usage);
        
        // 序列化为JSON并打印
        String json = JsonUtils.toJSONString(chatCompletion);
        System.out.println(json);
        System.out.println();
    }

    /**
     * 示例3：创建流式响应的开始chunk
     */
    public static void example3_StreamChunkStart() {
        System.out.println("=== 示例3：流式响应开始chunk ===");
        
        ChatCompletionChunk chunk = new ChatCompletionChunk();
        
        // 设置基本字段
        chunk.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", "").substring(0, 29));
        chunk.setObject("chat.completion.chunk");
        chunk.setCreated((int) Instant.now().getEpochSecond());
        chunk.setModel("gpt-4");
        
        // 创建choices
        List<ChatCompletionChunk.ChatCompletionChunkChoice> choices = new ArrayList<>();
        ChatCompletionChunk.ChatCompletionChunkChoice choice = new ChatCompletionChunk.ChatCompletionChunkChoice();
        choice.setIndex(0);
        
        // 创建delta，设置role
        ChatCompletionChunk.Delta delta = new ChatCompletionChunk.Delta();
        delta.setRole("assistant");
        choice.setDelta(delta);
        
        choices.add(choice);
        chunk.setChoices(choices);
        
        // 序列化为JSON并打印
        String json = JsonUtils.toJSONString(chunk);
        System.out.println(json);
        System.out.println();
    }

    /**
     * 示例4：创建流式响应的内容chunk
     */
    public static void example4_StreamChunkContent() {
        System.out.println("=== 示例4：流式响应内容chunk ===");
        
        ChatCompletionChunk chunk = new ChatCompletionChunk();
        
        // 设置基本字段
        chunk.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", "").substring(0, 29));
        chunk.setObject("chat.completion.chunk");
        chunk.setCreated((int) Instant.now().getEpochSecond());
        chunk.setModel("gpt-4");
        
        // 创建choices
        List<ChatCompletionChunk.ChatCompletionChunkChoice> choices = new ArrayList<>();
        ChatCompletionChunk.ChatCompletionChunkChoice choice = new ChatCompletionChunk.ChatCompletionChunkChoice();
        choice.setIndex(0);
        
        // 创建delta，设置content
        ChatCompletionChunk.Delta delta = new ChatCompletionChunk.Delta();
        delta.setContent("你好");
        choice.setDelta(delta);
        
        choices.add(choice);
        chunk.setChoices(choices);
        
        // 序列化为JSON并打印
        String json = JsonUtils.toJSONString(chunk);
        System.out.println(json);
        System.out.println();
    }

    /**
     * 示例5：创建流式响应的结束chunk
     */
    public static void example5_StreamChunkEnd() {
        System.out.println("=== 示例5：流式响应结束chunk ===");
        
        ChatCompletionChunk chunk = new ChatCompletionChunk();
        
        // 设置基本字段
        chunk.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", "").substring(0, 29));
        chunk.setObject("chat.completion.chunk");
        chunk.setCreated((int) Instant.now().getEpochSecond());
        chunk.setModel("gpt-4");
        
        // 创建choices
        List<ChatCompletionChunk.ChatCompletionChunkChoice> choices = new ArrayList<>();
        ChatCompletionChunk.ChatCompletionChunkChoice choice = new ChatCompletionChunk.ChatCompletionChunkChoice();
        choice.setIndex(0);
        choice.setFinishReason("stop");
        
        // 创建空的delta
        ChatCompletionChunk.Delta delta = new ChatCompletionChunk.Delta();
        choice.setDelta(delta);
        
        choices.add(choice);
        chunk.setChoices(choices);
        
        // 创建usage信息（通常在最后一个chunk中包含）
        ChatCompletionChunk.Usage usage = new ChatCompletionChunk.Usage();
        usage.setPromptTokens(8);
        usage.setCompletionTokens(15);
        usage.setTotalTokens(23);
        
        ChatCompletionChunk.PromptTokensDetails promptDetails = new ChatCompletionChunk.PromptTokensDetails();
        promptDetails.setCachedTokens(0);
        usage.setPromptTokensDetails(promptDetails);
        
        ChatCompletionChunk.CompletionTokensDetails completionDetails = new ChatCompletionChunk.CompletionTokensDetails();
        completionDetails.setReasoningTokens(0);
        usage.setCompletionTokensDetails(completionDetails);
        
        chunk.setUsage(usage);
        
        // 序列化为JSON并打印
        String json = JsonUtils.toJSONString(chunk);
        System.out.println(json);
        System.out.println();
    }

    /**
     * 运行所有示例
     */
    public static void main(String[] args) {
        example1_SimpleChatCompletion();
        example2_ChatCompletionWithToolCall();
        example3_StreamChunkStart();
        example4_StreamChunkContent();
        example5_StreamChunkEnd();
    }
}
