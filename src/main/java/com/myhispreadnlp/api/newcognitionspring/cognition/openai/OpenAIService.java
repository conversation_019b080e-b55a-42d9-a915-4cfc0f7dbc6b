package com.myhispreadnlp.api.newcognitionspring.cognition.openai;

import com.myhispreadnlp.api.newcognitionspring.channel.base.ChannelDispatchService;
import com.myhispreadnlp.api.newcognitionspring.channel.base.FallbackOption;
import com.myhispreadnlp.api.newcognitionspring.channel.base.RetryConfig;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.AzureRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.OpenaiAgentRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.OpenaiRemoteService;
import com.myhispreadnlp.api.newcognitionspring.common.exception.CognitionWebException;
import com.myhispreadnlp.api.newcognitionspring.common.utils.RateLimiter;
import com.myhispreadnlp.api.newcognitionspring.domain.context.ChatContext;
import com.myhispreadnlp.api.newcognitionspring.domain.error.ErrorConst;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("DuplicatedCode")
public class OpenAIService {

    private final RateLimiter rateLimiter;

    private final ChannelDispatchService channelDispatchService;

    private final OpenAIFallbackOptionsFactory fallbackOptionsFactory;
    private final OpenaiRemoteService openaiRemoteService;

    private final AzureRemoteService azureRemoteService;
    private final OpenaiAgentRemoteService openaiAgentRemoteService;


    public Flux<ServerSentEvent<String>> chatStream(HashMap<String, Object> req) {
        return Flux.deferContextual(contextView -> {
            processChatCompletion(req);
            ChatContext chatContext = contextView.get("chatContext");
            ServerWebExchange exchange = contextView.get("exchange");
            chatContext.setupRequest(req);
            if (checkRateLimit(chatContext, exchange)) {
                return Flux.error(new CognitionWebException(ErrorConst.RATE_LIMIT_EXCEEDED, HttpStatus.TOO_MANY_REQUESTS));
            } else {
                streamIncludeUsage(req);
                return channelDispatchService.streamCompletion(fallbackOptionsFactory.createStreamFallbackOptions(req, chatContext), chatContext);
            }
        });
    }

    public Mono<Object> chat(HashMap<String, Object> req) {
        return Mono.deferContextual(contextView -> {
            processChatCompletion(req);
            ChatContext chatContext = contextView.get("chatContext");
            ServerWebExchange exchange = contextView.get("exchange");
            chatContext.setupRequest(req);

            if (checkRateLimit(chatContext, exchange)) {
                return Mono.error(new CognitionWebException(ErrorConst.RATE_LIMIT_EXCEEDED, HttpStatus.TOO_MANY_REQUESTS));
            } else {
                return channelDispatchService.nonStreamCompletion(fallbackOptionsFactory.createFallbackOptions(req, chatContext), chatContext);
            }
        });
    }


    private void processChatCompletion(HashMap<String, Object> req) {
//        if (req.containsKey("max_completion_tokens") && req.containsKey("max_tokens")) {
//            return;
//        }
//        if (req.containsKey("max_completion_tokens")) {
//            req.put("max_tokens", req.get("max_completion_tokens"));
//            req.remove("max_completion_tokens");
//        }
    }


    /**
     * Checks if rate limiting needs to be applied based on the current chat context
     *
     * @param chatContext The current chat context
     * @param exchange    The server web exchange
     * @return true if rate limiting should be applied, false otherwise
     */
    private boolean checkRateLimit(ChatContext chatContext, ServerWebExchange exchange) {
        return rateLimiter.onRateLimitArrive(chatContext, exchange);
    }


    private static void streamIncludeUsage(HashMap<String, Object> request) {
        if (!request.containsKey("stream_options")) {
            request.put("stream_options", new HashMap<String, Object>() {{
                put("include_usage", true);
            }});
        }
    }

    public Mono<Object> response(HashMap<String, Object> req) {
        return Mono.deferContextual(contextView -> {
            ChatContext chatContext = contextView.get("chatContext");
            ServerWebExchange exchange = contextView.get("exchange");
            chatContext.setupRequest(req);

            if (checkRateLimit(chatContext, exchange)) {
                return Mono.error(new CognitionWebException(ErrorConst.RATE_LIMIT_EXCEEDED, HttpStatus.TOO_MANY_REQUESTS));
            } else {
                return channelDispatchService.nonStreamCompletion(List.of(
                                FallbackOption.of(() -> azureRemoteService.nonStreamOpenAiResponse(req, chatContext), RetryConfig.of(3)),
                                FallbackOption.of(() -> openaiRemoteService.nonStreamOpenAiResponse(req, chatContext), RetryConfig.of(3))
                        )
                        , chatContext);
            }
        });
    }

    public Flux<ServerSentEvent<String>> responseStream(HashMap<String, Object> req) {
        return Flux.deferContextual(contextView -> {
            ChatContext chatContext = contextView.get("chatContext");
            ServerWebExchange exchange = contextView.get("exchange");
            chatContext.setupRequest(req);
            if (checkRateLimit(chatContext, exchange)) {
                return Flux.error(new CognitionWebException(ErrorConst.RATE_LIMIT_EXCEEDED, HttpStatus.TOO_MANY_REQUESTS));
            } else {
                return channelDispatchService.streamCompletion(List.of(FallbackOption.of(() ->
                                azureRemoteService.streamOpenAiResponse(req, chatContext), RetryConfig.of(3)),
                        FallbackOption.of(() ->
                                openaiRemoteService.streamOpenAiResponse(req, chatContext), RetryConfig.of(3))
                ), chatContext);
            }
        });
    }

    public Mono<Object> imageGeneration(HashMap<String, Object> req) {
        return Mono.deferContextual(contextView -> {
            ChatContext chatContext = contextView.get("chatContext");
            ServerWebExchange exchange = contextView.get("exchange");
            chatContext.setupRequest(req);
            if (checkRateLimit(chatContext, exchange)) {
                return Mono.error(new CognitionWebException(ErrorConst.RATE_LIMIT_EXCEEDED, HttpStatus.TOO_MANY_REQUESTS));
            } else {
                return channelDispatchService.nonStreamCompletion(List.of(
                        FallbackOption.of(() -> openaiAgentRemoteService.imageGeneration(req, chatContext), RetryConfig.of(3)),
                        FallbackOption.of(() -> openaiRemoteService.imageGeneration(req, chatContext), RetryConfig.of(3))
                ), chatContext);
            }
        });
    }


    public Mono<Object> imageEdit(HashMap<String, Object> req) {
        return Mono.deferContextual(contextView -> {
            ChatContext chatContext = contextView.get("chatContext");
            ServerWebExchange exchange = contextView.get("exchange");
            chatContext.setupRequest(req);
            if (checkRateLimit(chatContext, exchange)) {
                return Mono.error(new CognitionWebException(ErrorConst.RATE_LIMIT_EXCEEDED, HttpStatus.TOO_MANY_REQUESTS));
            } else {
                return channelDispatchService.nonStreamCompletion(List.of(
                        FallbackOption.of(() -> openaiAgentRemoteService.imageEdit(req, chatContext), RetryConfig.of(3)),
                        FallbackOption.of(() -> openaiRemoteService.imageEdit(req, chatContext), RetryConfig.of(3))
                ), chatContext);
            }
        });
    }
}
