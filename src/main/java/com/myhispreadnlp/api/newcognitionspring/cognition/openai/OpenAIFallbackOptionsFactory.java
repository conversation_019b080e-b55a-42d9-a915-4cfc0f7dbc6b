package com.myhispreadnlp.api.newcognitionspring.cognition.openai;

import com.myhispreadnlp.api.newcognitionspring.channel.base.FallbackOption;
import com.myhispreadnlp.api.newcognitionspring.channel.base.RetryConfig;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.claude.AwsRawClaudeRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.AzureRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.ClaudeOpenAIRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.GeminiOpenAIRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.OpenaiAgentRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.OpenaiRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.transform.AwsOpenaiConverter;
import com.myhispreadnlp.api.newcognitionspring.domain.context.ChatContext;
import lombok.RequiredArgsConstructor;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Factory class for creating OpenAI fallback options
 */
@Component
@RequiredArgsConstructor
public class OpenAIFallbackOptionsFactory {


    private final AzureRemoteService azureRemoteService;
    private final OpenaiRemoteService openaiRemoteService;
    private final OpenaiAgentRemoteService openaiAgentRemoteService;
    private final ClaudeOpenAIRemoteService claudieOpenAIRemoteService;
    private final GeminiOpenAIRemoteService geminiOpenAIRemoteService;
    private final AwsRawClaudeRemoteService awsRawClaudeRemoteService;

    /**
     * Creates a list of fallback options for streaming OpenAI completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return List of fallback options
     */
    public List<FallbackOption<Flux<ServerSentEvent<String>>>> createStreamFallbackOptions(HashMap<String, Object> request, ChatContext chatContext) {
        if (chatContext.getChatModelInfo().onOtherModel() || chatContext.getChatModelInfo().onXModel()) {
            return List.of(
                    createOpenaiStreamFallbackOption(request, chatContext)
            );
        }
        if (chatContext.getChatModelInfo().onClaude()) {
            return List.of(
                    createClaudeStreamFallbackOption(request, chatContext)
            );
        }
        if (chatContext.getChatModelInfo().onGemini()) {
            return List.of(
                    createGeminiStreamFallbackOption(request, chatContext)
            );
        }
        return List.of(
                createAzureStreamFallbackOption(request, chatContext),
                createAgentStreamFallbackOption(request, chatContext),
                createOpenaiStreamFallbackOption(request, chatContext)
        );
    }

    /**
     * Creates a list of fallback options for non-streaming OpenAI completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return List of fallback options
     */
    public List<FallbackOption<Mono<Object>>> createFallbackOptions(HashMap<String, Object> request, ChatContext chatContext) {
        if (chatContext.getChatModelInfo().onOtherModel() || chatContext.getChatModelInfo().onXModel()) {
            return List.of(
                    createOpenaiNonStreamFallBackOption(request, chatContext)
            );
        }
        if (chatContext.getChatModelInfo().onClaude()) {
            return List.of(
                    createAwsClaudeNonStreamFallBackOption(request, chatContext)
            );
        }
        if (chatContext.getChatModelInfo().onGemini()) {
            return List.of(
                    createGeminiNonStreamFallBackOption(request, chatContext)
            );
        }
        return List.of(
                createAzureNonStreamFallBackOption(request, chatContext),
                createAgentNonStreamFallBackOption(request, chatContext),
                createOpenaiNonStreamFallBackOption(request, chatContext)
        );
    }

    /**
     * Creates a fallback option for streaming OpenAI completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Flux<ServerSentEvent<String>>> createOpenaiStreamFallbackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> openaiRemoteService.streamOpenAiCompletion(request, chatContext), RetryConfig.of(2));
    }

    /**
     * Creates a fallback option for non-streaming OpenAI completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Mono<Object>> createOpenaiNonStreamFallBackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> openaiRemoteService.nonStreamOpenAiCompletion(request, chatContext), RetryConfig.of(2));
    }

    /**
     * Creates a fallback option for streaming OpenAI agent completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Flux<ServerSentEvent<String>>> createAgentStreamFallbackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> openaiAgentRemoteService.streamOpenAiCompletion(request, chatContext), RetryConfig.of(3));
    }

    /**
     * Creates a fallback option for non-streaming OpenAI agent completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Mono<Object>> createAgentNonStreamFallBackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> openaiAgentRemoteService.nonStreamOpenAiCompletion(request, chatContext), RetryConfig.of(3));
    }

    /**
     * Creates a fallback option for streaming OpenAI agent completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Flux<ServerSentEvent<String>>> createAzureStreamFallbackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> azureRemoteService.streamAzureCompletion(request, chatContext), RetryConfig.of(2));
    }

    /**
     * Creates a fallback option for non-streaming OpenAI agent completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Mono<Object>> createAzureNonStreamFallBackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> azureRemoteService.nonStreamAzureCompletion(request, chatContext), RetryConfig.of(2));
    }


    /**
     * Creates a fallback option for streaming OpenAI agent completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Flux<ServerSentEvent<String>>> createClaudeStreamFallbackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> claudieOpenAIRemoteService.streamOpenAiCompletion(request, chatContext), RetryConfig.of(2));
    }

    /**
     * Creates a fallback option for non-streaming OpenAI agent completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Mono<Object>> createClaudeNonStreamFallBackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> claudieOpenAIRemoteService.nonStreamOpenAiCompletion(request, chatContext), RetryConfig.of(2));
    }

    /**
     * Creates a fallback option for streaming Gemini OpenAI completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Flux<ServerSentEvent<String>>> createGeminiStreamFallbackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> geminiOpenAIRemoteService.streamOpenAiCompletion(request, chatContext), RetryConfig.of(2));
    }

    /**
     * Creates a fallback option for non-streaming Gemini OpenAI completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Mono<Object>> createGeminiNonStreamFallBackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> geminiOpenAIRemoteService.nonStreamOpenAiCompletion(request, chatContext), RetryConfig.of(2));
    }


    /**
     * Creates a fallback option for streaming Gemini OpenAI completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Flux<ServerSentEvent<String>>> createAwsCLaudeStreamFallbackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() -> awsRawClaudeRemoteService.streamOpenAiCompletion(AwsOpenaiConverter.covertToAwsClaudeRequest(request), chatContext)
                        .map(x -> AwsOpenaiConverter.convertClaudeStreamToOpenAI( x))
                , RetryConfig.of(2));
    }

    /**
     * Creates a fallback option for non-streaming Gemini OpenAI completions
     *
     * @param request     The request parameters
     * @param chatContext The chat context
     * @return Fallback option
     */
    private FallbackOption<Mono<Object>> createAwsClaudeNonStreamFallBackOption(HashMap<String, Object> request, ChatContext chatContext) {
        return FallbackOption.of(() ->
                        awsRawClaudeRemoteService.nonStreamOpenAiCompletion(AwsOpenaiConverter.covertToAwsClaudeRequest(request), chatContext)
                                .map(x -> AwsOpenaiConverter.covertToOpenAIResponse((HashMap<String, Object>) x))
                , RetryConfig.of(2)
        );
    }

}
