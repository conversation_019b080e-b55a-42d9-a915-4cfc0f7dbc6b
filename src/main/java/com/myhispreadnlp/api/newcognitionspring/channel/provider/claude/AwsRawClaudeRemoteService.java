package com.myhispreadnlp.api.newcognitionspring.channel.provider.claude;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.myhispreadnlp.api.newcognitionspring.cache.CognitionKeysCache;
import com.myhispreadnlp.api.newcognitionspring.cache.CognitionModelCache;
import com.myhispreadnlp.api.newcognitionspring.channel.enums.ChannelType;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.LlmRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.claude.aws.req.BedrockRequest;
import com.myhispreadnlp.api.newcognitionspring.common.exception.CognitionWebException;
import com.myhispreadnlp.api.newcognitionspring.common.utils.BedrockApiInvoker;
import com.myhispreadnlp.api.newcognitionspring.common.utils.JsonUtils;
import com.myhispreadnlp.api.newcognitionspring.domain.context.ChatContext;
import com.myhispreadnlp.api.newcognitionspring.domain.context.usage.ChatUsage;
import com.myhispreadnlp.api.newcognitionspring.domain.response.anthropic.ClaudeMessageTokenStats;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("ALL")
public class AwsRawClaudeRemoteService extends LlmRemoteService {

    private static final String streamURI = "https://bedrock-runtime.%s.amazonaws.com/model/%s/invoke-with-response-stream";
    private static final String nonStreamURI = "https://bedrock-runtime.%s.amazonaws.com/model/%s/invoke";
    @Qualifier("gcpHttpClient")
    private final WebClient httpWebClient;
    @Qualifier("gcpStreamClient")
    private final WebClient sseWebClient;
    private final ObjectMapper objectMapper;
    private final CognitionModelCache cognitionModelCache;
    private final CognitionKeysCache cognitionKeysCache;

    private Function<String, ServerSentEvent<String>> process(ChatContext chatContext, AtomicBoolean isFirst) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();
        return message -> {

            if (isFirst.getAndSet(false)) {
                chatContext.getChatRequestStatistic().setPreparationTime(Instant.now().toEpochMilli());
                log.debug("收到首个数据块 | 用户ID: {} | 模型: {}", userId, modelName);
            }
            try {

                // Log detailed response at DEBUG level to avoid excessive logging
                log.debug("接收数据块 | 用户ID: {} | 模型: {} | 消息内容: {}", userId, modelName, message);

                var jsonNode = JsonUtils.parseJson(message);
                var type = jsonNode.path("type").asText();
                if ("message_start".equalsIgnoreCase(type)) {
                    var usage = objectMapper.convertValue(jsonNode.path("message").path("usage"), ClaudeMessageTokenStats.class);
                    chatContext.setChatUsage(ChatUsage.create(usage));
                    log.info("更新使用情况 | 请求类型: 流式 | 用户ID: {} | 模型: {} | 使用量: {}", userId, modelName, JsonUtils.toJSONString(usage));
                }

                if ("message_delta".equalsIgnoreCase(type)) {
                    var outputTokens = jsonNode.path("usage").path("output_tokens").asInt();
                    chatContext.getChatUsage().updateOutPut(outputTokens);
                }

                log.debug("数据块解析完成 | 用户ID: {} | 模型: {} | 响应内容: {}",
                        userId, modelName, message);
                return ServerSentEvent.builder(message).event(type).build();
            } catch (Exception e) {
                log.error("数据块解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException("Error occurred with OpenAI API stream parse to chatCompletionChunk: " + e.getMessage());
            }
        };
    }

    private static int parseStreamInputUsage(String body) {
        try {
            JsonNode rootNode = JsonUtils.mapper.readTree(body);
            JsonNode usageNode = rootNode.path("message").path("usage");
            return usageNode.path("input_tokens").asInt();
        } catch (Exception e) {
            log.error("Error parsing usage tokens: {}", e.getMessage());
            return 0;
        }
    }

    private static int parseStreamOutUsage(String body) {
        try {
            // 移除 "data: " 前缀
            JsonNode rootNode = JsonUtils.mapper.readTree(body);
            JsonNode usageNode = rootNode.path("usage");
            return usageNode.path("output_tokens").asInt();
        } catch (Exception e) {
            log.error("Error parsing  GoogleClaude usage tokens: {}", e.getMessage());
            return 0;
        }
    }

    private void parseUsage(String body, ChatContext chatContext) {
        log.debug("接收非流式响应 | 用户ID: {} | 模型: {} | 消息内容: {}", chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), body);
        var usage = objectMapper.convertValue(JsonUtils.parseJson(body).path("usage"), ClaudeMessageTokenStats.class);

        if (usage != null) {
            chatContext.setChatUsage(ChatUsage.create(usage));
            log.info("更新使用情况 | 请求类型: 非流式 | 用户ID: {} | 模型: {} | 使用量: {}", chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), JsonUtils.toJSONString(usage));
        }
    }

    public static void addResponseHeader(String body, ChatContext charactersContext, WebClient.RequestBodySpec requestBodyUriSpec, String url) {
        var timestamp = ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'"));
        var cache = charactersContext.getCurrentApiConfig();
        String secret = cache.getSecret();
        String cognitionKey = cache.getKey();
        String region = cache.getRegion();
        var request = new BedrockRequest(
                cognitionKey,
                secret,
                region,
                "bedrock",
                body,
                timestamp,
                UUID.randomUUID().toString()
        );
        var response = BedrockApiInvoker.invokeBedrockApi(request, url);
        response.getHeaders().forEach(requestBodyUriSpec::header);
    }

    public static String encodeURL(String url) {
        return URLEncoder.encode(url, StandardCharsets.UTF_8);
    }

    private static String parseEventContent(String content) {
        try {
            var jsonContent = getString(content);
            JsonNode jsonNode = JsonUtils.parseJson(jsonContent);
            String bytesBase64 = jsonNode.get("bytes").asText();
            byte[] decodedBytes = Base64.getDecoder().decode(bytesBase64);
            return new String(decodedBytes);
        } catch (Exception e) {
            System.out.println("解析事件内容时发生错误: " + e.getMessage());
            return "";
        }
    }

    private static String getString(String content) {
        int jsonStart = content.indexOf("{");
        int bracketCount = 0;
        int jsonEnd = jsonStart;
        for (int i = jsonStart; i < content.length(); i++) {
            if (content.charAt(i) == '{') {
                bracketCount++;
            } else if (content.charAt(i) == '}') {
                bracketCount--;
            }
            if (bracketCount == 0) {
                jsonEnd = i + 1;
                break;
            }
        }
        return content.substring(jsonStart, jsonEnd);
    }

    public Flux<ServerSentEvent<String>> streamOpenAiCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        AtomicBoolean isFirst = new AtomicBoolean(true);
        return Flux.create(emitter ->
                Mono.fromCallable(() -> {
                            cognitionKeysCache.setupKey(chatContext, ChannelType.AWS);
                            log.info("开始流式请求 | 用户ID: {} | 模型: {} | 域名: {} | 密钥: {}",
                                    chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(),
                                    chatContext.getCurrentApiConfig().getDomain(),
                                    chatContext.getCurrentApiConfig().getKey());
                            return chatContext.getCurrentApiConfig();
                        })
                        .doFirst(() -> {
                            body.remove("model");
                            body.remove("stream");
                            body.put("anthropic_version", "bedrock-2023-05-31");
                        })
                        .flatMapMany(cache -> {
                                    String region = cache.getRegion();
                                    var uri = String.format(streamURI, region, encodeURL(chatContext.apiModelName()));
                                    var req = JsonUtils.toJSONString(body);
                                    WebClient.RequestBodySpec clientReqSpec = null;
                                    try {
                                        clientReqSpec = sseWebClient.post().uri(new URI(uri));
                                    } catch (URISyntaxException e) {
                                        throw new RuntimeException(e);
                                    }
                                    addResponseHeader(req, chatContext, clientReqSpec, uri);
                                    return clientReqSpec
                                            .bodyValue(req)
                                            .retrieve()
                                            .bodyToFlux(byte[].class)
                                            .map(bytes -> new String(bytes, StandardCharsets.UTF_8))
                                            .scan(new StringBuilder(), (acc, value) -> {
                                                acc.append(value);
                                                int newlineIndex;
                                                while ((newlineIndex = acc.indexOf("\r")) != -1) {
                                                    String line = acc.substring(0, newlineIndex);
                                                    acc.delete(0, newlineIndex + 1);
                                                    if (!line.isBlank()) {
                                                        if (line.contains(":message-type")) {
                                                            String content = parseEventContent(line);
                                                            emitter.next(process(chatContext, isFirst).apply(content));
                                                        }
                                                    }
                                                }
                                                return acc;
                                            })
                                            .doOnError(error -> {
                                                log.error("Error occurred with GoogleClaude API stream: {}", error.getMessage());
                                            })
                                            .doFinally(signal -> {
                                                log.info("AWS API stream completed:{} ", signal);
                                            });
                                }
                        )
                        .doOnError(error -> {
                            emitter.error(error);
                            loggingError(error, chatContext, ChannelType.AWS);
                        })
                        .doFinally(signal -> {
                            emitter.complete();
                            log.info("AWS API stream completed:{} ", signal);
                        })
                        .subscribe()
        );
    }

    public Mono<Object> nonStreamOpenAiCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        return Mono.fromCallable(() -> {
                    cognitionKeysCache.setupKey(chatContext, ChannelType.AWS);
                    log.info("开始流式请求 | 用户ID: {} | 模型: {} | 域名: {} | 密钥: {}",
                            chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(),
                            chatContext.getCurrentApiConfig().getDomain(),
                            chatContext.getCurrentApiConfig().getKey());
                    return chatContext.getCurrentApiConfig();
                })
                .doFirst(() -> {
                    body.remove("model");
                    body.remove("stream");
                    body.put("anthropic_version", "bedrock-2023-05-31");
                })
                .flatMap(cache -> {
                            String region = cache.getRegion();
                            var uri = String.format(nonStreamURI, region, encodeURL(chatContext.apiModelName()));
                            var req = JsonUtils.toJSONString(body);
                            WebClient.RequestBodySpec clientReqSpec = null;
                            try {
                                clientReqSpec = httpWebClient.post().uri(new URI(uri));
                            } catch (URISyntaxException e) {
                                throw new RuntimeException(e);
                            }
                            addResponseHeader(req, chatContext, clientReqSpec, uri);
                            return clientReqSpec
                                    .bodyValue(req)
                                    .retrieve()
                                    .bodyToMono(String.class)
                                    .doOnCancel(() -> {
                                        log.warn("请求被取消或超时");
                                        throw new RuntimeException("请求被取消或超时");
                                    })
                                    .map(message -> {
                                        parseUsage(message, chatContext);
                                        try {
                                            return objectMapper.readValue(message, Object.class);
                                        } catch (JsonProcessingException e) {
                                            log.error("AWS NONStream 数据块解析错误", e);
                                            throw new RuntimeException(e);
                                        }
                                    }).doFinally(signal -> {
                                        log.info("GoogleClaude API completed:{} ", signal);
                                    });
                        }
                )
                .doOnError(error -> {
                    loggingError(error, chatContext, ChannelType.AWS);
                });
    }

}
