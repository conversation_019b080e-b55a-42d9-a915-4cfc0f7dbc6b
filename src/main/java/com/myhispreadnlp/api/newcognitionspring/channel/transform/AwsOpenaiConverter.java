package com.myhispreadnlp.api.newcognitionspring.channel.transform;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.myhispreadnlp.api.newcognitionspring.common.utils.JsonUtils;
import com.myhispreadnlp.api.newcognitionspring.domain.response.anthropic.ClaudeMessageTokenStats;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatCompletion;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatCompletionChunk;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.*;

/**
 * AWS Claude消息API与OpenAI Chat Completion API之间的转换器
 */
@Slf4j
public class AwsOpenaiConverter {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将OpenAI格式的请求转换为AWS Claude消息API格式
     *
     * @param openaiFormatRequest OpenAI格式的请求
     * @return AWS Claude消息API格式的请求
     */
    public static HashMap<String, Object> covertToAwsClaudeRequest(HashMap<String, Object> openaiFormatRequest) {
        HashMap<String, Object> claudeRequest = new HashMap<>();

        try {
            // 复制基本参数
            if (openaiFormatRequest.containsKey("max_tokens")) {
                claudeRequest.put("max_tokens", openaiFormatRequest.get("max_tokens"));
            }
            if (openaiFormatRequest.containsKey("temperature")) {
                claudeRequest.put("temperature", openaiFormatRequest.get("temperature"));
            }
            if (openaiFormatRequest.containsKey("top_p")) {
                claudeRequest.put("top_p", openaiFormatRequest.get("top_p"));
            }
            if (openaiFormatRequest.containsKey("stop")) {
                claudeRequest.put("stop_sequences", openaiFormatRequest.get("stop"));
            }

            // 处理messages
            Object messagesObj = openaiFormatRequest.get("messages");
            if (messagesObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> messages = (List<Map<String, Object>>) messagesObj;

                List<Map<String, Object>> claudeMessages = new ArrayList<>();
                String systemMessage = null;

                for (Map<String, Object> message : messages) {
                    String role = (String) message.get("role");
                    Object contentObj = message.get("content");

                    // 处理content字段，可能是字符串或数组
                    Object processedContent = processMessageContent(contentObj);

                    if ("system".equals(role)) {
                        // system消息的content必须是字符串
                        if (processedContent instanceof String) {
                            systemMessage = (String) processedContent;
                        } else if (processedContent instanceof List) {
                            // 如果system消息是数组格式，提取文本内容
                            systemMessage = extractTextFromContentArray((List<?>) processedContent);
                        }
                    } else if ("user".equals(role) || "assistant".equals(role)) {
                        Map<String, Object> claudeMessage = new HashMap<>();
                        claudeMessage.put("role", role);
                        claudeMessage.put("content", processedContent);
                        claudeMessages.add(claudeMessage);
                    }
                }

                claudeRequest.put("messages", claudeMessages);

                // 如果有system消息，单独设置
                if (systemMessage != null) {
                    claudeRequest.put("system", systemMessage);
                }
            }

            // 设置Claude特有的参数
            claudeRequest.put("anthropic_version", "bedrock-2023-05-31");

            // 处理流式参数
            if (openaiFormatRequest.containsKey("stream")) {
                claudeRequest.put("stream", openaiFormatRequest.get("stream"));
            }

        } catch (Exception e) {
            log.error("转换OpenAI请求到AWS Claude格式时发生错误: {}", e.getMessage(), e);
        }

        return claudeRequest;
    }

    /**
     * 将AWS Claude格式的响应转换为OpenAI格式
     *
     * @param awsClaudeFormatResponse AWS Claude格式的响应
     * @return OpenAI格式的响应
     */
    public static Object covertToOpenAIResponse(HashMap<String, Object> awsClaudeFormatResponse) {
        try {
            // 使用ChatCompletion类构建响应
            ChatCompletion chatCompletion = createChatCompletionFromClaude(awsClaudeFormatResponse);

            return chatCompletion;

        } catch (Exception e) {
            log.error("转换AWS Claude响应到OpenAI格式时发生错误: {}", e.getMessage(), e);
            // 降级处理：返回基本的响应结构
            return createFallbackResponse(awsClaudeFormatResponse);
        }
    }

    /**
     * 从Claude响应创建ChatCompletion对象
     */
    private static ChatCompletion createChatCompletionFromClaude(HashMap<String, Object> claudeResponse) {
        ChatCompletion chatCompletion = new ChatCompletion();

        // 设置基本信息
        chatCompletion.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", "").substring(0, 29));
        chatCompletion.setObject("chat.completion");
        chatCompletion.setCreated((int) Instant.now().getEpochSecond());
        chatCompletion.setModel((String) claudeResponse.getOrDefault("model", "claude-3-sonnet"));

        // 创建choices
        List<ChatCompletion.ChatCompletionChoice> choices = new ArrayList<>();
        ChatCompletion.ChatCompletionChoice choice = new ChatCompletion.ChatCompletionChoice();
        choice.setIndex(0);
        choice.setFinishReason((String) claudeResponse.getOrDefault("stop_reason", "stop"));

        // 创建message
        ChatCompletion.ChatMessage message = new ChatCompletion.ChatMessage();
        message.setRole("assistant");

        // 从Claude响应中提取content
        String content = extractContentFromClaude(claudeResponse.get("content"));
        message.setContent(content);

        choice.setMessage(message);
        choices.add(choice);
        chatCompletion.setChoices(choices);

        // 处理usage信息
        Object usageObj = claudeResponse.get("usage");
        if (usageObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> claudeUsage = (Map<String, Object>) usageObj;

            ChatCompletion.Usage usage = new ChatCompletion.Usage();
            usage.setPromptTokens((Integer) claudeUsage.getOrDefault("input_tokens", 0));
            usage.setCompletionTokens((Integer) claudeUsage.getOrDefault("output_tokens", 0));
            usage.setTotalTokens(usage.getPromptTokens() + usage.getCompletionTokens());

            // 设置详细的token信息
            ChatCompletion.PromptTokensDetails promptDetails = new ChatCompletion.PromptTokensDetails();
            promptDetails.setCachedTokens((Integer) claudeUsage.getOrDefault("cache_read_input_tokens", 0));
            usage.setPromptTokensDetails(promptDetails);

            ChatCompletion.CompletionTokensDetails completionDetails = new ChatCompletion.CompletionTokensDetails();
            usage.setCompletionTokensDetails(completionDetails);

            chatCompletion.setUsage(usage);
        }

        return chatCompletion;
    }

    /**
     * 从Claude的content中提取文本内容
     */
    private static String extractContentFromClaude(Object contentObj) {
        if (contentObj instanceof String) {
            return (String) contentObj;
        } else if (contentObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> contentList = (List<Map<String, Object>>) contentObj;
            StringBuilder contentBuilder = new StringBuilder();

            for (Map<String, Object> contentItem : contentList) {
                if ("text".equals(contentItem.get("type"))) {
                    Object text = contentItem.get("text");
                    if (text != null) {
                        contentBuilder.append(text.toString());
                    }
                }
            }

            return contentBuilder.toString();
        } else if (contentObj != null) {
            return contentObj.toString();
        } else {
            return "";
        }
    }

    /**
     * 创建降级响应（当转换失败时使用）
     */
    private static HashMap<String, Object> createFallbackResponse(HashMap<String, Object> claudeResponse) {
        HashMap<String, Object> openaiResponse = new HashMap<>();

        openaiResponse.put("id", "chatcmpl-" + UUID.randomUUID().toString().replace("-", "").substring(0, 29));
        openaiResponse.put("object", "chat.completion");
        openaiResponse.put("created", (int) Instant.now().getEpochSecond());
        openaiResponse.put("model", claudeResponse.getOrDefault("model", "claude-3-sonnet"));

        // 简单的choices结构
        List<Map<String, Object>> choices = new ArrayList<>();
        Map<String, Object> choice = new HashMap<>();
        choice.put("index", 0);
        choice.put("finish_reason", "stop");

        Map<String, Object> message = new HashMap<>();
        message.put("role", "assistant");
        message.put("content", extractContentFromClaude(claudeResponse.get("content")));
        choice.put("message", message);

        choices.add(choice);
        openaiResponse.put("choices", choices);

        return openaiResponse;
    }

    /**
     * 将AWS Claude流式响应转换为OpenAI ChatCompletionChunk格式
     *
     * @param claudeResponse AWS Claude的流式响应JSON字符串
     * @param modelName 模型名称
     * @return OpenAI ChatCompletionChunk格式的JSON字符串
     */
    public static String convertClaudeStreamToOpenAI(String claudeResponse, String modelName) {
        try {
            JsonNode claudeNode = JsonUtils.parseJson(claudeResponse);
            String type = claudeNode.path("type").asText();

            // 使用ChatCompletionChunk类构建响应
            ChatCompletionChunk chunk = createChatCompletionChunkFromClaude(claudeNode, modelName, type);

            return JsonUtils.toJSONString(chunk);

        } catch (Exception e) {
            log.error("转换Claude流式响应到OpenAI格式时发生错误: {}", e.getMessage(), e);
            return claudeResponse; // 转换失败时返回原始响应
        }
    }

    /**
     * 从Claude流式响应创建ChatCompletionChunk对象
     */
    private static ChatCompletionChunk createChatCompletionChunkFromClaude(JsonNode claudeNode, String modelName, String type) {
        ChatCompletionChunk chunk = new ChatCompletionChunk();

        // 设置基本信息
        chunk.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", "").substring(0, 29));
        chunk.setObject("chat.completion.chunk");
        chunk.setCreated((int) Instant.now().getEpochSecond());
        chunk.setModel(modelName);

        // 创建choices
        List<ChatCompletionChunk.ChatCompletionChunkChoice> choices = new ArrayList<>();
        ChatCompletionChunk.ChatCompletionChunkChoice choice = new ChatCompletionChunk.ChatCompletionChunkChoice();
        choice.setIndex(0);

        ChatCompletionChunk.Delta delta = new ChatCompletionChunk.Delta();

        switch (type) {
            case "message_start":
                // 消息开始，设置role
                delta.setRole("assistant");
                choice.setDelta(delta);

                // 处理usage信息
                JsonNode usageNode = claudeNode.path("message").path("usage");
                if (!usageNode.isMissingNode()) {
                    ClaudeMessageTokenStats claudeUsage = objectMapper.convertValue(usageNode, ClaudeMessageTokenStats.class);
                    ChatCompletionChunk.Usage usage = convertClaudeUsageToOpenAI(claudeUsage);
                    chunk.setUsage(usage);
                }
                break;

            case "content_block_start":
                // 内容块开始，通常不需要特殊处理
                choice.setDelta(delta);
                break;

            case "content_block_delta":
                // 内容增量更新
                JsonNode deltaNode = claudeNode.path("delta");
                if (deltaNode.has("text")) {
                    delta.setContent(deltaNode.path("text").asText());
                }
                choice.setDelta(delta);
                break;

            case "content_block_stop":
                // 内容块结束
                choice.setDelta(delta);
                break;

            case "message_delta":
                // 消息增量更新，通常包含usage信息
                JsonNode messageDeltaUsage = claudeNode.path("usage");
                if (!messageDeltaUsage.isMissingNode()) {
                    ClaudeMessageTokenStats claudeUsage = objectMapper.convertValue(messageDeltaUsage, ClaudeMessageTokenStats.class);
                    ChatCompletionChunk.Usage usage = convertClaudeUsageToOpenAI(claudeUsage);
                    chunk.setUsage(usage);
                }
                choice.setDelta(delta);
                break;

            case "message_stop":
                // 消息结束
                choice.setFinishReason("stop");
                choice.setDelta(delta);
                break;

            default:
                // 未知类型，返回空delta
                choice.setDelta(delta);
                break;
        }

        choices.add(choice);
        chunk.setChoices(choices);

        return chunk;
    }

    /**
     * 将Claude的usage信息转换为OpenAI的usage格式
     *
     * @param claudeUsage Claude的token统计信息
     * @return OpenAI格式的usage信息
     */
    private static ChatCompletionChunk.Usage convertClaudeUsageToOpenAI(ClaudeMessageTokenStats claudeUsage) {
        ChatCompletionChunk.Usage usage = new ChatCompletionChunk.Usage();

        usage.setPromptTokens(claudeUsage.getInputTokens());
        usage.setCompletionTokens(claudeUsage.getOutputTokens());
        usage.setTotalTokens(claudeUsage.getInputTokens() + claudeUsage.getOutputTokens());

        // 设置详细的token信息
        ChatCompletionChunk.PromptTokensDetails promptDetails = new ChatCompletionChunk.PromptTokensDetails();
        promptDetails.setCachedTokens(claudeUsage.getCacheReadInputTokens());
        usage.setPromptTokensDetails(promptDetails);

        ChatCompletionChunk.CompletionTokensDetails completionDetails = new ChatCompletionChunk.CompletionTokensDetails();
        usage.setCompletionTokensDetails(completionDetails);

        return usage;
    }

    /**
     * 将OpenAI流式响应转换为Claude格式（反向转换）
     *
     * @param openaiChunk OpenAI ChatCompletionChunk格式的JSON字符串
     * @return Claude格式的流式响应JSON字符串
     */
    public static String convertOpenAIStreamToClaude(String openaiChunk) {
        try {
            ChatCompletionChunk chunk = JsonUtils.parseObject(openaiChunk, ChatCompletionChunk.class);

            Map<String, Object> claudeResponse = new HashMap<>();

            if (chunk.getChoices() != null && !chunk.getChoices().isEmpty()) {
                ChatCompletionChunk.ChatCompletionChunkChoice choice = chunk.getChoices().get(0);
                ChatCompletionChunk.Delta delta = choice.getDelta();

                if (delta != null) {
                    if (delta.getRole() != null) {
                        // 消息开始
                        claudeResponse.put("type", "message_start");
                        Map<String, Object> message = new HashMap<>();
                        message.put("role", delta.getRole());
                        claudeResponse.put("message", message);
                    } else if (delta.getContent() != null) {
                        // 内容增量
                        claudeResponse.put("type", "content_block_delta");
                        Map<String, Object> deltaMap = new HashMap<>();
                        deltaMap.put("text", delta.getContent());
                        claudeResponse.put("delta", deltaMap);
                    } else if (choice.getFinishReason() != null) {
                        // 消息结束
                        claudeResponse.put("type", "message_stop");
                    }
                }
            }

            // 处理usage信息
            if (chunk.getUsage() != null) {
                Map<String, Object> usage = new HashMap<>();
                usage.put("input_tokens", chunk.getUsage().getPromptTokens());
                usage.put("output_tokens", chunk.getUsage().getCompletionTokens());
                claudeResponse.put("usage", usage);
            }

            return JsonUtils.toJSONString(claudeResponse);

        } catch (Exception e) {
            log.error("转换OpenAI流式响应到Claude格式时发生错误: {}", e.getMessage(), e);
            return openaiChunk; // 转换失败时返回原始响应
        }
    }

    /**
     * 处理消息内容，支持字符串和数组格式
     *
     * @param contentObj 原始content对象
     * @return 处理后的content对象
     */
    private static Object processMessageContent(Object contentObj) {
        if (contentObj == null) {
            return "";
        }

        if (contentObj instanceof String) {
            // 如果是字符串，直接返回
            return contentObj;
        } else if (contentObj instanceof List) {
            // 如果是数组，保持数组格式（Claude也支持数组格式的content）
            return contentObj;
        } else {
            // 其他类型转换为字符串
            return contentObj.toString();
        }
    }

    /**
     * 从content数组中提取文本内容（用于system消息）
     *
     * @param contentArray content数组
     * @return 提取的文本内容
     */
    private static String extractTextFromContentArray(List<?> contentArray) {
        StringBuilder textBuilder = new StringBuilder();

        for (Object item : contentArray) {
            if (item instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> contentItem = (Map<String, Object>) item;
                String type = (String) contentItem.get("type");

                if ("text".equals(type)) {
                    Object text = contentItem.get("text");
                    if (text != null) {
                        textBuilder.append(text.toString());
                    }
                }
            }
        }

        return textBuilder.toString();
    }
}
