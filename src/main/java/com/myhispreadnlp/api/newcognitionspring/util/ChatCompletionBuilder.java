package com.myhispreadnlp.api.newcognitionspring.util;

import com.myhispreadnlp.api.newcognitionspring.common.utils.JsonUtils;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatCompletion;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatCompletionChunk;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * ChatCompletion和ChatCompletionChunk构建工具类
 */
public class ChatCompletionBuilder {

    /**
     * 构建基础的ChatCompletion对象
     */
    public static ChatCompletionBuilderCopy chatCompletion() {
        return new ChatCompletionBuilderCopy();
    }

    /**
     * 构建基础的ChatCompletionChunk对象
     */
    public static ChatCompletionChunkBuilder chatCompletionChunk() {
        return new ChatCompletionChunkBuilder();
    }

    /**
     * ChatCompletion构建器
     */
    public static class ChatCompletionBuilderCopy {
        private final ChatCompletion chatCompletion;

        public ChatCompletionBuilderCopy() {
            this.chatCompletion = new ChatCompletion();
            // 设置默认值
            this.chatCompletion.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", "").substring(0, 29));
            this.chatCompletion.setObject("chat.completion");
            this.chatCompletion.setCreated((int) Instant.now().getEpochSecond());
            this.chatCompletion.setChoices(new ArrayList<>());
        }

        public ChatCompletionBuilderCopy id(String id) {
            this.chatCompletion.setId(id);
            return this;
        }

        public ChatCompletionBuilderCopy model(String model) {
            this.chatCompletion.setModel(model);
            return this;
        }

        public ChatCompletionBuilderCopy systemFingerprint(String systemFingerprint) {
            this.chatCompletion.setSystemFingerprint(systemFingerprint);
            return this;
        }

        public ChatCompletionBuilderCopy addChoice(int index, String role, String content, String finishReason) {
            ChatCompletion.ChatCompletionChoice choice = new ChatCompletion.ChatCompletionChoice();
            choice.setIndex(index);
            choice.setFinishReason(finishReason);

            ChatCompletion.ChatMessage message = new ChatCompletion.ChatMessage();
            message.setRole(role);
            message.setContent(content);
            choice.setMessage(message);

            this.chatCompletion.getChoices().add(choice);
            return this;
        }

        public ChatCompletionBuilderCopy addAssistantChoice(String content) {
            return addChoice(this.chatCompletion.getChoices().size(), "assistant", content, "stop");
        }

        public ChatCompletionBuilderCopy addToolCallChoice(String toolCallId, String functionName, String arguments) {
            ChatCompletion.ChatCompletionChoice choice = new ChatCompletion.ChatCompletionChoice();
            choice.setIndex(this.chatCompletion.getChoices().size());
            choice.setFinishReason("tool_calls");

            ChatCompletion.ChatMessage message = new ChatCompletion.ChatMessage();
            message.setRole("assistant");
            message.setContent(null);

            // 创建tool call
            List<ChatCompletion.ToolCall> toolCalls = new ArrayList<>();
            ChatCompletion.ToolCall toolCall = new ChatCompletion.ToolCall();
            toolCall.setId(toolCallId);
            toolCall.setType("function");

            ChatCompletion.Function function = new ChatCompletion.Function();
            function.setName(functionName);
            function.setArguments(arguments);
            toolCall.setFunction(function);

            toolCalls.add(toolCall);
            message.setToolCalls(toolCalls);
            choice.setMessage(message);

            this.chatCompletion.getChoices().add(choice);
            return this;
        }

        public ChatCompletionBuilderCopy usage(int promptTokens, int completionTokens) {
            ChatCompletion.Usage usage = new ChatCompletion.Usage();
            usage.setPromptTokens(promptTokens);
            usage.setCompletionTokens(completionTokens);
            usage.setTotalTokens(promptTokens + completionTokens);

            ChatCompletion.PromptTokensDetails promptDetails = new ChatCompletion.PromptTokensDetails();
            usage.setPromptTokensDetails(promptDetails);

            ChatCompletion.CompletionTokensDetails completionDetails = new ChatCompletion.CompletionTokensDetails();
            usage.setCompletionTokensDetails(completionDetails);

            this.chatCompletion.setUsage(usage);
            return this;
        }

        public ChatCompletionBuilderCopy usage(int promptTokens, int completionTokens, int cachedTokens) {
            usage(promptTokens, completionTokens);
            this.chatCompletion.getUsage().getPromptTokensDetails().setCachedTokens(cachedTokens);
            return this;
        }

        public ChatCompletion build() {
            return this.chatCompletion;
        }

        public String buildJson() {
            return JsonUtils.toJSONString(build());
        }
    }

    /**
     * ChatCompletionChunk构建器
     */
    public static class ChatCompletionChunkBuilder {
        private final ChatCompletionChunk chunk;

        public ChatCompletionChunkBuilder() {
            this.chunk = new ChatCompletionChunk();
            // 设置默认值
            this.chunk.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", "").substring(0, 29));
            this.chunk.setObject("chat.completion.chunk");
            this.chunk.setCreated((int) Instant.now().getEpochSecond());
            this.chunk.setChoices(new ArrayList<>());
        }

        public ChatCompletionChunkBuilder id(String id) {
            this.chunk.setId(id);
            return this;
        }

        public ChatCompletionChunkBuilder model(String model) {
            this.chunk.setModel(model);
            return this;
        }

        public ChatCompletionChunkBuilder addDelta(int index, String role, String content, String finishReason) {
            ChatCompletionChunk.ChatCompletionChunkChoice choice = new ChatCompletionChunk.ChatCompletionChunkChoice();
            choice.setIndex(index);
            choice.setFinishReason(finishReason);

            ChatCompletionChunk.Delta delta = new ChatCompletionChunk.Delta();
            delta.setRole(role);
            delta.setContent(content);
            choice.setDelta(delta);

            this.chunk.getChoices().add(choice);
            return this;
        }

        public ChatCompletionChunkBuilder addRoleDelta(String role) {
            return addDelta(0, role, null, null);
        }

        public ChatCompletionChunkBuilder addContentDelta(String content) {
            return addDelta(0, null, content, null);
        }

        public ChatCompletionChunkBuilder addFinishDelta(String finishReason) {
            return addDelta(0, null, null, finishReason);
        }

        public ChatCompletionChunkBuilder usage(int promptTokens, int completionTokens) {
            ChatCompletionChunk.Usage usage = new ChatCompletionChunk.Usage();
            usage.setPromptTokens(promptTokens);
            usage.setCompletionTokens(completionTokens);
            usage.setTotalTokens(promptTokens + completionTokens);

            ChatCompletionChunk.PromptTokensDetails promptDetails = new ChatCompletionChunk.PromptTokensDetails();
            usage.setPromptTokensDetails(promptDetails);

            ChatCompletionChunk.CompletionTokensDetails completionDetails = new ChatCompletionChunk.CompletionTokensDetails();
            usage.setCompletionTokensDetails(completionDetails);

            this.chunk.setUsage(usage);
            return this;
        }

        public ChatCompletionChunk build() {
            return this.chunk;
        }

        public String buildJson() {
            return JsonUtils.toJSONString(build());
        }
    }

    /**
     * 快速创建简单的ChatCompletion响应
     */
    public static String createSimpleResponse(String model, String content) {
        return chatCompletion()
                .model(model)
                .addAssistantChoice(content)
                .usage(10, content.length() / 4) // 简单的token估算
                .buildJson();
    }

    /**
     * 快速创建流式响应的开始chunk
     */
    public static String createStreamStart(String model) {
        return chatCompletionChunk()
                .model(model)
                .addRoleDelta("assistant")
                .buildJson();
    }

    /**
     * 快速创建流式响应的内容chunk
     */
    public static String createStreamContent(String model, String content) {
        return chatCompletionChunk()
                .model(model)
                .addContentDelta(content)
                .buildJson();
    }

    /**
     * 快速创建流式响应的结束chunk
     */
    public static String createStreamEnd(String model, int promptTokens, int completionTokens) {
        return chatCompletionChunk()
                .model(model)
                .addFinishDelta("stop")
                .usage(promptTokens, completionTokens)
                .buildJson();
    }
}
